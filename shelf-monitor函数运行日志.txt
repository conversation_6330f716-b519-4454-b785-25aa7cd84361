请求入参: {
    "args": {
      "userPayload": "",
      "triggerTime": "2025-08-16T08:28:00Z",
      "triggerName": "TIMER_LATEST"
    },
    "requestId": "timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8"
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/2ms/DEBUG] 货架监控任务开始执行 2025-08-16T08:28:00.669Z
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/194ms/DEBUG] 开始处理用户 687a6b72ce5ec9e58d9b118b 的 2 个平台
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/691ms/DEBUG] 开始处理账号 lilonghaoshuai6 的 1 个货架
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/691ms/DEBUG] BaseAdapter constructor {
    _id: '687d01e26523417562eb9ee8',
    user_id: '687a6b72ce5ec9e58d9b118b',
    platform_name: 'U号租',
    platform_type: 'uhaozu',
    username: '17602911442',
    password: 'lilong5201230.',
    token: 's+LRIZFz54HmEeGctfLQAQ==',
    cookie: '',
    headers: {},
    auto_login: true,
    login_status: 1,
    status: 1,
    remark: '',
    update_time: 2025-08-14T14:06:19.948Z,
    create_time: 2025-07-20T14:49:05.986Z,
    last_login_time: 2025-08-14T14:06:19.948Z
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/691ms/DEBUG] BaseAdapter constructor {
    _id: '687a74bb337a9f5121efb022',
    user_id: '687a6b72ce5ec9e58d9b118b',
    platform_name: '租号玩',
    platform_type: 'zuhaowan',
    username: 'looong',
    password: '5201230.',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTU0MTkxNjcsInN1YiI6bnVsbCwibmJmIjoxNzU1MzMyNzY3LCJhdWQiOm51bGwsImlhdCI6MTc1NTMzMjc2NywianRpIjoiU1Y5ZGhHQWpvZyIsImlzcyI6IkVhc3lTd29vbGUiLCJzdGF0dXMiOjEsImRhdGEiOnsidXNlcm5hbWUiOiJsb29vbmciLCJ0eXBlIjoyLCJpZCI6MTAxMTQzLCJuYW1lIjoi5p2O6b6ZIn19.0mAntBczf8Nl7qTla32S4de6Dm-yN8AUgKw4zKGa9Yk',
    cookie: '',
    headers: {},
    auto_login: true,
    login_status: 1,
    status: 1,
    remark: '',
    update_time: 2025-08-16T08:26:12.915Z,
    create_time: 2025-07-18T16:22:17.531Z,
    last_login_time: 2025-08-16T08:26:12.914Z
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/692ms/DEBUG] 获取uhaozu平台的完整货架列表
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/692ms/DEBUG] 发送请求: POST https://api.uhaozu.com/tool/goods/list/all
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/879ms/DEBUG] 请求成功: https://api.uhaozu.com/tool/goods/list/all
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/879ms/DEBUG] U号租登录状态检测: {
    object: [
      {
        carrierName: '',
        gameAccount: 'lilonghaoshuai6',
        gameId: '578080',
        gameName: '绝地求生',
        gameRoleName: 'UE0006',
        goodsId: **********,
        goodsStatus: 4,
        goodsTitle: '3600+皮⭐23神器·满内购丨瀚娜·⑩火麒麟·满级圣诞AK·满女团丨最新装备【UE0006】',
        minRentTime: 2,
        rentStatus: 1,
        rentalByHour: 800
      },
      {
        carrierName: '',
        gameAccount: 'fwchedmk',
        gameId: '578080',
        gameName: '绝地求生',
        gameRoleName: 'UE0004',
        goodsId: **********,
        goodsStatus: 3,
        goodsTitle: '【令牌看图】8神器4满丨异色无信号妖魔之触牛头AUG丨网鱼夹克·汉娜·多内购【UE0004】',
        minRentTime: 2,
        rentStatus: 1,
        rentalByHour: 500
      }
    ],
    responseCode: '0000',
    responseMsg: 'success',
    success: true
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/5884ms/DEBUG] 延迟返回响应: https://api.uhaozu.com/tool/goods/list/all
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/5884ms/DEBUG] U号租货架列表响应: {"object":[{"carrierName":"","gameAccount":"lilonghaoshuai6","gameId":"578080","gameName":"绝地求生","gameRoleName":"UE0006","goodsId":**********,"goodsStatus":4,"goodsTitle":"3600+皮⭐23神器·满内购丨瀚娜·⑩火麒麟·满级圣诞AK·满女团丨最新装备【UE0006】","minRentTime":2,"rentStatus":1,"rentalByHour":800},{"carrierName":"","gameAccount":"fwchedmk","gameId":"578080","gameName":"绝地求生","gameRoleName":"UE0004","goodsId":**********,"goodsStatus":3,"goodsTitle":"【令牌看图】8神器4满丨异色无信号妖魔之触牛头AUG丨网鱼夹克·汉娜·多内购【UE0004】","minRentTime":2,"rentStatus":1,"rentalByHour":500}],"responseCode":"0000","responseMsg":"success","success":true}
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/5884ms/DEBUG] 获取zuhaowan平台的完整货架列表
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/5885ms/DEBUG] 发送请求: POST https://zu.zuhaowan.com/api/Account/search
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/6049ms/DEBUG] 请求成功: https://zu.zuhaowan.com/api/Account/search
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/6049ms/DEBUG] 租号玩登录状态检测: {
    code: 200,
    status: 200,
    data: {
      list: [ [Object], [Object], [Object], [Object] ],
      count: 4,
      account_count: {
        all: 4,
        deleted: 3,
        hide: 0,
        noPass: 0,
        offRent: 1,
        onRent: 1,
        verify: 0,
        wait: 2
      },
      changeSwitch: 1,
      hand_act_info_switch: '1',
      hao_score_switch: 0,
      quick_client_v22_pc_open: '1',
      sync_role_to_remark_switch: '1',
      description: null,
      sublet_price_bat_btn: 0
    },
    message: ''
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/11055ms/DEBUG] 延迟返回响应: https://zu.zuhaowan.com/api/Account/search
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/11056ms/ERROR] 更新货架状态失败 zuhaowan: TypeError: adapter.getShelfStatus is not a function
      at processAccountShelves (/tmp/function/__index.js:189:45)
      at processUserPlatforms (/tmp/function/__index.js:113:7)
      at exports.main (/tmp/function/__index.js:47:7)
      at $e (/tmp/function/index.js:1:85303)
      at Ve (/tmp/function/index.js:1:86420)
      at runUserFunction (/code/index.js:93:265116)
      at (/code/index.js:93:259283)
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/11353ms/DEBUG] [sync] zuhaowan - adapter.getShelfStatus is not a function
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/11353ms/DEBUG] 账号 lilonghaoshuai6 没有出租，检查是否需要重新上架
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/11353ms/DEBUG] 开始处理账号 fwchedmk 的 1 个货架
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/11354ms/DEBUG] BaseAdapter constructor {
    _id: '687d01e26523417562eb9ee8',
    user_id: '687a6b72ce5ec9e58d9b118b',
    platform_name: 'U号租',
    platform_type: 'uhaozu',
    username: '17602911442',
    password: 'lilong5201230.',
    token: 's+LRIZFz54HmEeGctfLQAQ==',
    cookie: '',
    headers: {},
    auto_login: true,
    login_status: 1,
    status: 1,
    remark: '',
    update_time: 2025-08-14T14:06:19.948Z,
    create_time: 2025-07-20T14:49:05.986Z,
    last_login_time: 2025-08-14T14:06:19.948Z
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/11354ms/DEBUG] BaseAdapter constructor {
    _id: '687a74bb337a9f5121efb022',
    user_id: '687a6b72ce5ec9e58d9b118b',
    platform_name: '租号玩',
    platform_type: 'zuhaowan',
    username: 'looong',
    password: '5201230.',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTU0MTkxNjcsInN1YiI6bnVsbCwibmJmIjoxNzU1MzMyNzY3LCJhdWQiOm51bGwsImlhdCI6MTc1NTMzMjc2NywianRpIjoiU1Y5ZGhHQWpvZyIsImlzcyI6IkVhc3lTd29vbGUiLCJzdGF0dXMiOjEsImRhdGEiOnsidXNlcm5hbWUiOiJsb29vbmciLCJ0eXBlIjoyLCJpZCI6MTAxMTQzLCJuYW1lIjoi5p2O6b6ZIn19.0mAntBczf8Nl7qTla32S4de6Dm-yN8AUgKw4zKGa9Yk',
    cookie: '',
    headers: {},
    auto_login: true,
    login_status: 1,
    status: 1,
    remark: '',
    update_time: 2025-08-16T08:26:12.915Z,
    create_time: 2025-07-18T16:22:17.531Z,
    last_login_time: 2025-08-16T08:26:12.914Z
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/11354ms/DEBUG] 获取uhaozu平台的完整货架列表
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/11354ms/DEBUG] 发送请求: POST https://api.uhaozu.com/tool/goods/list/all
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/11517ms/DEBUG] 请求成功: https://api.uhaozu.com/tool/goods/list/all
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/11517ms/DEBUG] U号租登录状态检测: {
    object: [
      {
        carrierName: '',
        gameAccount: 'lilonghaoshuai6',
        gameId: '578080',
        gameName: '绝地求生',
        gameRoleName: 'UE0006',
        goodsId: **********,
        goodsStatus: 4,
        goodsTitle: '3600+皮⭐23神器·满内购丨瀚娜·⑩火麒麟·满级圣诞AK·满女团丨最新装备【UE0006】',
        minRentTime: 2,
        rentStatus: 1,
        rentalByHour: 800
      },
      {
        carrierName: '',
        gameAccount: 'fwchedmk',
        gameId: '578080',
        gameName: '绝地求生',
        gameRoleName: 'UE0004',
        goodsId: **********,
        goodsStatus: 3,
        goodsTitle: '【令牌看图】8神器4满丨异色无信号妖魔之触牛头AUG丨网鱼夹克·汉娜·多内购【UE0004】',
        minRentTime: 2,
        rentStatus: 1,
        rentalByHour: 500
      }
    ],
    responseCode: '0000',
    responseMsg: 'success',
    success: true
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/16522ms/DEBUG] 延迟返回响应: https://api.uhaozu.com/tool/goods/list/all
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/16522ms/DEBUG] U号租货架列表响应: {"object":[{"carrierName":"","gameAccount":"lilonghaoshuai6","gameId":"578080","gameName":"绝地求生","gameRoleName":"UE0006","goodsId":**********,"goodsStatus":4,"goodsTitle":"3600+皮⭐23神器·满内购丨瀚娜·⑩火麒麟·满级圣诞AK·满女团丨最新装备【UE0006】","minRentTime":2,"rentStatus":1,"rentalByHour":800},{"carrierName":"","gameAccount":"fwchedmk","gameId":"578080","gameName":"绝地求生","gameRoleName":"UE0004","goodsId":**********,"goodsStatus":3,"goodsTitle":"【令牌看图】8神器4满丨异色无信号妖魔之触牛头AUG丨网鱼夹克·汉娜·多内购【UE0004】","minRentTime":2,"rentStatus":1,"rentalByHour":500}],"responseCode":"0000","responseMsg":"success","success":true}
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/16522ms/DEBUG] 获取zuhaowan平台的完整货架列表
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/16522ms/DEBUG] 发送请求: POST https://zu.zuhaowan.com/api/Account/search
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/16660ms/DEBUG] 请求成功: https://zu.zuhaowan.com/api/Account/search
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/16661ms/DEBUG] 租号玩登录状态检测: {
    code: 200,
    status: 200,
    data: {
      list: [ [Object], [Object], [Object], [Object] ],
      count: 4,
      account_count: {
        all: 4,
        deleted: 3,
        hide: 0,
        noPass: 0,
        offRent: 1,
        onRent: 1,
        verify: 0,
        wait: 2
      },
      changeSwitch: 1,
      hand_act_info_switch: '1',
      hao_score_switch: 0,
      quick_client_v22_pc_open: '1',
      sync_role_to_remark_switch: '1',
      description: null,
      sublet_price_bat_btn: 0
    },
    message: ''
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/21666ms/DEBUG] 延迟返回响应: https://zu.zuhaowan.com/api/Account/search
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/21667ms/ERROR] 更新货架状态失败 zuhaowan: TypeError: adapter.getShelfStatus is not a function
      at processAccountShelves (/tmp/function/__index.js:189:45)
      at processUserPlatforms (/tmp/function/__index.js:113:7)
      at exports.main (/tmp/function/__index.js:47:7)
      at $e (/tmp/function/index.js:1:85303)
      at Ve (/tmp/function/index.js:1:86420)
      at runUserFunction (/code/index.js:93:265116)
      at (/code/index.js:93:259283)
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/21860ms/DEBUG] [sync] zuhaowan - adapter.getShelfStatus is not a function
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/21860ms/DEBUG] 账号 fwchedmk 没有出租，检查是否需要重新上架
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/21860ms/DEBUG] 开始处理账号 qma79123 的 1 个货架
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/21860ms/DEBUG] BaseAdapter constructor {
    _id: '687d01e26523417562eb9ee8',
    user_id: '687a6b72ce5ec9e58d9b118b',
    platform_name: 'U号租',
    platform_type: 'uhaozu',
    username: '17602911442',
    password: 'lilong5201230.',
    token: 's+LRIZFz54HmEeGctfLQAQ==',
    cookie: '',
    headers: {},
    auto_login: true,
    login_status: 1,
    status: 1,
    remark: '',
    update_time: 2025-08-14T14:06:19.948Z,
    create_time: 2025-07-20T14:49:05.986Z,
    last_login_time: 2025-08-14T14:06:19.948Z
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/21861ms/DEBUG] BaseAdapter constructor {
    _id: '687a74bb337a9f5121efb022',
    user_id: '687a6b72ce5ec9e58d9b118b',
    platform_name: '租号玩',
    platform_type: 'zuhaowan',
    username: 'looong',
    password: '5201230.',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTU0MTkxNjcsInN1YiI6bnVsbCwibmJmIjoxNzU1MzMyNzY3LCJhdWQiOm51bGwsImlhdCI6MTc1NTMzMjc2NywianRpIjoiU1Y5ZGhHQWpvZyIsImlzcyI6IkVhc3lTd29vbGUiLCJzdGF0dXMiOjEsImRhdGEiOnsidXNlcm5hbWUiOiJsb29vbmciLCJ0eXBlIjoyLCJpZCI6MTAxMTQzLCJuYW1lIjoi5p2O6b6ZIn19.0mAntBczf8Nl7qTla32S4de6Dm-yN8AUgKw4zKGa9Yk',
    cookie: '',
    headers: {},
    auto_login: true,
    login_status: 1,
    status: 1,
    remark: '',
    update_time: 2025-08-16T08:26:12.915Z,
    create_time: 2025-07-18T16:22:17.531Z,
    last_login_time: 2025-08-16T08:26:12.914Z
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/21861ms/DEBUG] 获取uhaozu平台的完整货架列表
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/21861ms/DEBUG] 发送请求: POST https://api.uhaozu.com/tool/goods/list/all
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/22023ms/DEBUG] 请求成功: https://api.uhaozu.com/tool/goods/list/all
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/22023ms/DEBUG] U号租登录状态检测: {
    object: [
      {
        carrierName: '',
        gameAccount: 'lilonghaoshuai6',
        gameId: '578080',
        gameName: '绝地求生',
        gameRoleName: 'UE0006',
        goodsId: **********,
        goodsStatus: 4,
        goodsTitle: '3600+皮⭐23神器·满内购丨瀚娜·⑩火麒麟·满级圣诞AK·满女团丨最新装备【UE0006】',
        minRentTime: 2,
        rentStatus: 1,
        rentalByHour: 800
      },
      {
        carrierName: '',
        gameAccount: 'fwchedmk',
        gameId: '578080',
        gameName: '绝地求生',
        gameRoleName: 'UE0004',
        goodsId: **********,
        goodsStatus: 3,
        goodsTitle: '【令牌看图】8神器4满丨异色无信号妖魔之触牛头AUG丨网鱼夹克·汉娜·多内购【UE0004】',
        minRentTime: 2,
        rentStatus: 1,
        rentalByHour: 500
      }
    ],
    responseCode: '0000',
    responseMsg: 'success',
    success: true
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/27028ms/DEBUG] 延迟返回响应: https://api.uhaozu.com/tool/goods/list/all
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/27028ms/DEBUG] U号租货架列表响应: {"object":[{"carrierName":"","gameAccount":"lilonghaoshuai6","gameId":"578080","gameName":"绝地求生","gameRoleName":"UE0006","goodsId":**********,"goodsStatus":4,"goodsTitle":"3600+皮⭐23神器·满内购丨瀚娜·⑩火麒麟·满级圣诞AK·满女团丨最新装备【UE0006】","minRentTime":2,"rentStatus":1,"rentalByHour":800},{"carrierName":"","gameAccount":"fwchedmk","gameId":"578080","gameName":"绝地求生","gameRoleName":"UE0004","goodsId":**********,"goodsStatus":3,"goodsTitle":"【令牌看图】8神器4满丨异色无信号妖魔之触牛头AUG丨网鱼夹克·汉娜·多内购【UE0004】","minRentTime":2,"rentStatus":1,"rentalByHour":500}],"responseCode":"0000","responseMsg":"success","success":true}
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/27028ms/DEBUG] 获取zuhaowan平台的完整货架列表
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/27029ms/DEBUG] 发送请求: POST https://zu.zuhaowan.com/api/Account/search
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/27183ms/DEBUG] 请求成功: https://zu.zuhaowan.com/api/Account/search
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/27183ms/DEBUG] 租号玩登录状态检测: {
    code: 200,
    status: 200,
    data: {
      list: [ [Object], [Object], [Object], [Object] ],
      count: 4,
      account_count: {
        all: 4,
        deleted: 3,
        hide: 0,
        noPass: 0,
        offRent: 1,
        onRent: 1,
        verify: 0,
        wait: 2
      },
      changeSwitch: 1,
      hand_act_info_switch: '1',
      hao_score_switch: 0,
      quick_client_v22_pc_open: '1',
      sync_role_to_remark_switch: '1',
      description: null,
      sublet_price_bat_btn: 0
    },
    message: ''
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/32184ms/DEBUG] 延迟返回响应: https://zu.zuhaowan.com/api/Account/search
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/32184ms/ERROR] 更新货架状态失败 zuhaowan: TypeError: adapter.getShelfStatus is not a function
      at processAccountShelves (/tmp/function/__index.js:189:45)
      at processUserPlatforms (/tmp/function/__index.js:113:7)
      at exports.main (/tmp/function/__index.js:47:7)
      at $e (/tmp/function/index.js:1:85303)
      at Ve (/tmp/function/index.js:1:86420)
      at runUserFunction (/code/index.js:93:265116)
      at (/code/index.js:93:259283)
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/32380ms/DEBUG] [sync] zuhaowan - adapter.getShelfStatus is not a function
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/32380ms/DEBUG] 账号 qma79123 没有出租，检查是否需要重新上架
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/32380ms/DEBUG] 开始处理账号 pjkqnd_f 的 1 个货架
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/32380ms/DEBUG] BaseAdapter constructor {
    _id: '687d01e26523417562eb9ee8',
    user_id: '687a6b72ce5ec9e58d9b118b',
    platform_name: 'U号租',
    platform_type: 'uhaozu',
    username: '17602911442',
    password: 'lilong5201230.',
    token: 's+LRIZFz54HmEeGctfLQAQ==',
    cookie: '',
    headers: {},
    auto_login: true,
    login_status: 1,
    status: 1,
    remark: '',
    update_time: 2025-08-14T14:06:19.948Z,
    create_time: 2025-07-20T14:49:05.986Z,
    last_login_time: 2025-08-14T14:06:19.948Z
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/32380ms/DEBUG] BaseAdapter constructor {
    _id: '687a74bb337a9f5121efb022',
    user_id: '687a6b72ce5ec9e58d9b118b',
    platform_name: '租号玩',
    platform_type: 'zuhaowan',
    username: 'looong',
    password: '5201230.',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTU0MTkxNjcsInN1YiI6bnVsbCwibmJmIjoxNzU1MzMyNzY3LCJhdWQiOm51bGwsImlhdCI6MTc1NTMzMjc2NywianRpIjoiU1Y5ZGhHQWpvZyIsImlzcyI6IkVhc3lTd29vbGUiLCJzdGF0dXMiOjEsImRhdGEiOnsidXNlcm5hbWUiOiJsb29vbmciLCJ0eXBlIjoyLCJpZCI6MTAxMTQzLCJuYW1lIjoi5p2O6b6ZIn19.0mAntBczf8Nl7qTla32S4de6Dm-yN8AUgKw4zKGa9Yk',
    cookie: '',
    headers: {},
    auto_login: true,
    login_status: 1,
    status: 1,
    remark: '',
    update_time: 2025-08-16T08:26:12.915Z,
    create_time: 2025-07-18T16:22:17.531Z,
    last_login_time: 2025-08-16T08:26:12.914Z
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/32381ms/DEBUG] 获取uhaozu平台的完整货架列表
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/32381ms/DEBUG] 发送请求: POST https://api.uhaozu.com/tool/goods/list/all
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/32517ms/DEBUG] 请求成功: https://api.uhaozu.com/tool/goods/list/all
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/32517ms/DEBUG] U号租登录状态检测: {
    object: [
      {
        carrierName: '',
        gameAccount: 'lilonghaoshuai6',
        gameId: '578080',
        gameName: '绝地求生',
        gameRoleName: 'UE0006',
        goodsId: **********,
        goodsStatus: 4,
        goodsTitle: '3600+皮⭐23神器·满内购丨瀚娜·⑩火麒麟·满级圣诞AK·满女团丨最新装备【UE0006】',
        minRentTime: 2,
        rentStatus: 1,
        rentalByHour: 800
      },
      {
        carrierName: '',
        gameAccount: 'fwchedmk',
        gameId: '578080',
        gameName: '绝地求生',
        gameRoleName: 'UE0004',
        goodsId: **********,
        goodsStatus: 3,
        goodsTitle: '【令牌看图】8神器4满丨异色无信号妖魔之触牛头AUG丨网鱼夹克·汉娜·多内购【UE0004】',
        minRentTime: 2,
        rentStatus: 1,
        rentalByHour: 500
      }
    ],
    responseCode: '0000',
    responseMsg: 'success',
    success: true
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/37522ms/DEBUG] 延迟返回响应: https://api.uhaozu.com/tool/goods/list/all
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/37523ms/DEBUG] U号租货架列表响应: {"object":[{"carrierName":"","gameAccount":"lilonghaoshuai6","gameId":"578080","gameName":"绝地求生","gameRoleName":"UE0006","goodsId":**********,"goodsStatus":4,"goodsTitle":"3600+皮⭐23神器·满内购丨瀚娜·⑩火麒麟·满级圣诞AK·满女团丨最新装备【UE0006】","minRentTime":2,"rentStatus":1,"rentalByHour":800},{"carrierName":"","gameAccount":"fwchedmk","gameId":"578080","gameName":"绝地求生","gameRoleName":"UE0004","goodsId":**********,"goodsStatus":3,"goodsTitle":"【令牌看图】8神器4满丨异色无信号妖魔之触牛头AUG丨网鱼夹克·汉娜·多内购【UE0004】","minRentTime":2,"rentStatus":1,"rentalByHour":500}],"responseCode":"0000","responseMsg":"success","success":true}
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/37523ms/DEBUG] 获取zuhaowan平台的完整货架列表
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/37523ms/DEBUG] 发送请求: POST https://zu.zuhaowan.com/api/Account/search
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/37674ms/DEBUG] 请求成功: https://zu.zuhaowan.com/api/Account/search
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/37674ms/DEBUG] 租号玩登录状态检测: {
    code: 200,
    status: 200,
    data: {
      list: [ [Object], [Object], [Object], [Object] ],
      count: 4,
      account_count: {
        all: 4,
        deleted: 3,
        hide: 0,
        noPass: 0,
        offRent: 1,
        onRent: 1,
        verify: 0,
        wait: 2
      },
      changeSwitch: 1,
      hand_act_info_switch: '1',
      hao_score_switch: 0,
      quick_client_v22_pc_open: '1',
      sync_role_to_remark_switch: '1',
      description: null,
      sublet_price_bat_btn: 0
    },
    message: ''
  }
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/42679ms/DEBUG] 延迟返回响应: https://zu.zuhaowan.com/api/Account/search
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/42680ms/ERROR] 更新货架状态失败 zuhaowan: TypeError: adapter.getShelfStatus is not a function
      at processAccountShelves (/tmp/function/__index.js:189:45)
      at processUserPlatforms (/tmp/function/__index.js:113:7)
      at exports.main (/tmp/function/__index.js:47:7)
      at $e (/tmp/function/index.js:1:85303)
      at Ve (/tmp/function/index.js:1:86420)
      at runUserFunction (/code/index.js:93:265116)
      at (/code/index.js:93:259283)
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/42960ms/DEBUG] [sync] zuhaowan - adapter.getShelfStatus is not a function
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/42960ms/DEBUG] 账号 pjkqnd_f 没有出租，检查是否需要重新上架
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/43201ms/DEBUG] 清理过期日志完成，保留1天
  [shelf-monitor/timer1755332880465-284f2f81-4c4c-426e-9784-713f613a88d8/43202ms/DEBUG] 货架监控任务执行完成，耗时: 43200ms
  请求响应状态: success
  